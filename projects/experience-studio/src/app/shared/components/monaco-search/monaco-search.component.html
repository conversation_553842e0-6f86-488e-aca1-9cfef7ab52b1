<div class="monaco-search-widget" [class.visible]="isVisible()" *ngIf="isVisible()">
  <div class="search-container">
    <!-- Search Input Section -->
    <div class="search-input-section">
      <div class="search-input-wrapper">
        <exp-icons
          iconName="awe_search"
          [iconColor]="iconColor()"
          class="search-icon">
        </exp-icons>
        
        <input
          #searchInput
          type="text"
          class="search-input"
          placeholder="Search in file..."
          [value]="searchQuery()"
          (keydown)="onSearchKeydown($event)"
          autocomplete="off"
          spellcheck="false"
        />
        
        <div class="search-results-info" *ngIf="searchQuery()">
          <span class="results-text">{{ getMatchDisplayText() }}</span>
        </div>
        
        <button
          class="clear-search-btn"
          (click)="clearSearch()"
          *ngIf="searchQuery()"
          title="Clear search"
          type="button">
          <exp-icons iconName="awe_close" [iconColor]="iconColor()"></exp-icons>
        </button>
      </div>
      
      <!-- Search Options -->
      <div class="search-options">
        <button
          class="search-option-btn"
          [class.active]="searchState().caseSensitive"
          (click)="toggleCaseSensitive()"
          title="Match Case (Alt+C)"
          type="button">
          <span class="option-text">Aa</span>
        </button>
        
        <button
          class="search-option-btn"
          [class.active]="searchState().wholeWord"
          (click)="toggleWholeWord()"
          title="Match Whole Word (Alt+W)"
          type="button">
          <span class="option-text">Ab</span>
        </button>
        
        <button
          class="search-option-btn"
          [class.active]="searchState().useRegex"
          (click)="toggleRegex()"
          title="Use Regular Expression (Alt+R)"
          type="button">
          <span class="option-text">.*</span>
        </button>
      </div>
    </div>
    
    <!-- Navigation Controls -->
    <div class="search-navigation">
      <button
        class="nav-btn"
        (click)="findPrevious()"
        [disabled]="!hasResults()"
        title="Previous match (Shift+Enter)"
        type="button">
        <exp-icons iconName="awe_chevron_up" [iconColor]="iconColor()"></exp-icons>
      </button>
      
      <button
        class="nav-btn"
        (click)="findNext()"
        [disabled]="!hasResults()"
        title="Next match (Enter)"
        type="button">
        <exp-icons iconName="awe_chevron_down" [iconColor]="iconColor()"></exp-icons>
      </button>
    </div>
  </div>
  
  <!-- Loading Indicator -->
  <div class="search-loading" *ngIf="isSearching()">
    <div class="loading-spinner"></div>
  </div>
</div>
