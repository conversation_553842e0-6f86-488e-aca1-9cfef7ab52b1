import {
  Component,
  ChangeDetectionStrategy,
  ViewEncapsulation,
  ElementRef,
  ViewChild,
  inject,
  signal,
  effect,
  computed,
  OnInit,
  OnDestroy,
  DestroyRef,
  ChangeDetectorRef
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { fromEvent, debounceTime, distinctUntilChanged } from 'rxjs';

import { MonacoSearchService } from '../../services/monaco-search.service';
import { IconsComponent } from '../icons/icons.component';
import { ThemeService } from '../../services/theme-service/theme.service';

@Component({
  selector: 'app-monaco-search',
  standalone: true,
  imports: [CommonModule, FormsModule, IconsComponent],
  templateUrl: './monaco-search.component.html',
  styleUrls: ['./monaco-search.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None
})
export class MonacoSearchComponent implements OnInit, OnDestroy {
  @ViewChild('searchInput', { static: true }) searchInput!: ElementRef<HTMLInputElement>;

  // Injected services
  private monacoSearchService = inject(MonacoSearchService);
  private themeService = inject(ThemeService);
  private destroyRef = inject(DestroyRef);
  private cdr = inject(ChangeDetectorRef);

  // Component state signals
  isVisible = signal(false);
  searchQuery = signal('');
  currentTheme = signal<'light' | 'dark'>('light');

  // Search state from service
  searchState = this.monacoSearchService.searchState;

  // Computed theme-aware icon colors
  iconColor = computed(() => this.currentTheme() === 'dark' ? 'whiteIcon' : 'neutralIcon');

  constructor() {
    // Initialize theme
    this.currentTheme.set(this.themeService.getCurrentTheme());

    // React to search state changes
    effect(() => {
      const state = this.searchState();
      // Update local search query if it differs (for external updates)
      if (state.query !== this.searchQuery()) {
        this.searchQuery.set(state.query);
      }
    });

    // Subscribe to theme changes
    this.themeService.themeObservable
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(theme => {
        this.currentTheme.set(theme);
        this.updateThemeClass(theme);
        this.cdr.markForCheck();
      });
  }

  ngOnInit(): void {
    this.setupSearchInput();
    this.setupKeyboardShortcuts();
  }

  ngOnDestroy(): void {
    this.hide();
  }

  /**
   * Update theme class on the search widget
   */
  private updateThemeClass(theme: 'light' | 'dark'): void {
    // The theme is handled via CSS custom properties and data-theme attribute
    // which is set at the document level by the theme service
  }

  /**
   * Show the search widget
   */
  show(): void {
    this.isVisible.set(true);
    // Focus the search input after a short delay to ensure it's rendered
    setTimeout(() => {
      this.searchInput?.nativeElement?.focus();
    }, 50);
  }

  /**
   * Hide the search widget
   */
  hide(): void {
    this.isVisible.set(false);
    this.monacoSearchService.clearSearch();
    this.searchQuery.set('');
  }

  /**
   * Toggle the search widget visibility
   */
  toggle(): void {
    if (this.isVisible()) {
      this.hide();
    } else {
      this.show();
    }
  }

  /**
   * Handle search input changes
   */
  onSearchInput(query: string): void {
    this.searchQuery.set(query);
    if (query.trim()) {
      const state = this.searchState();
      this.monacoSearchService.search(query, {
        caseSensitive: state.caseSensitive,
        wholeWord: state.wholeWord,
        useRegex: state.useRegex
      });
    } else {
      this.monacoSearchService.clearSearch();
    }
  }

  /**
   * Navigate to next match
   */
  findNext(): void {
    this.monacoSearchService.findNext();
  }

  /**
   * Navigate to previous match
   */
  findPrevious(): void {
    this.monacoSearchService.findPrevious();
  }

  /**
   * Toggle case sensitive search
   */
  toggleCaseSensitive(): void {
    this.monacoSearchService.toggleCaseSensitive();
  }

  /**
   * Toggle whole word search
   */
  toggleWholeWord(): void {
    this.monacoSearchService.toggleWholeWord();
  }

  /**
   * Toggle regex search
   */
  toggleRegex(): void {
    this.monacoSearchService.toggleRegex();
  }

  /**
   * Clear the search
   */
  clearSearch(): void {
    this.searchQuery.set('');
    this.monacoSearchService.clearSearch();
  }

  /**
   * Handle keyboard events in search input
   */
  onSearchKeydown(event: KeyboardEvent): void {
    switch (event.key) {
      case 'Enter':
        event.preventDefault();
        if (event.shiftKey) {
          this.findPrevious();
        } else {
          this.findNext();
        }
        break;
      case 'Escape':
        event.preventDefault();
        this.hide();
        break;
      case 'F3':
        event.preventDefault();
        if (event.shiftKey) {
          this.findPrevious();
        } else {
          this.findNext();
        }
        break;
    }
  }

  /**
   * Get the current match display text
   */
  getMatchDisplayText(): string {
    const state = this.searchState();
    if (state.totalMatches === 0) {
      return state.query ? 'No results' : '';
    }
    return `${state.currentMatchIndex + 1} of ${state.totalMatches}`;
  }

  /**
   * Check if search has results
   */
  hasResults(): boolean {
    return this.searchState().totalMatches > 0;
  }

  /**
   * Check if search is in progress
   */
  isSearching(): boolean {
    return this.searchState().isSearching;
  }

  /**
   * Setup debounced search input
   */
  private setupSearchInput(): void {
    if (!this.searchInput?.nativeElement) return;

    fromEvent(this.searchInput.nativeElement, 'input')
      .pipe(
        debounceTime(300),
        distinctUntilChanged(),
        takeUntilDestroyed()
      )
      .subscribe((event: any) => {
        this.onSearchInput(event.target.value);
      });
  }

  /**
   * Setup global keyboard shortcuts
   */
  private setupKeyboardShortcuts(): void {
    fromEvent<KeyboardEvent>(document, 'keydown')
      .pipe(takeUntilDestroyed())
      .subscribe((event) => {
        // Only handle shortcuts when focus is within the code viewer area
        const target = event.target as HTMLElement;
        const isInCodeViewer = target.closest('.editor-container') || target.closest('.monaco-search-widget');

        if (!isInCodeViewer) return;

        // Ctrl+F or Cmd+F to open search
        if ((event.ctrlKey || event.metaKey) && event.key === 'f') {
          event.preventDefault();
          this.show();
        }

        // F3 for find next/previous (when search is visible)
        if (event.key === 'F3' && this.isVisible()) {
          event.preventDefault();
          if (event.shiftKey) {
            this.findPrevious();
          } else {
            this.findNext();
          }
        }

        // Escape to close search
        if (event.key === 'Escape' && this.isVisible()) {
          event.preventDefault();
          this.hide();
        }
      });
  }
}
